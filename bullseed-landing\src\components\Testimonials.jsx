import React from 'react';

const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Portfolio Manager',
      avatar: '👩‍💼',
      text: 'The platform\'s ability to handle complex trading strategies while maintaining simplicity in its interface is remarkable. It\'s been invaluable for our portfolio management.'
    },
    {
      name: '<PERSON>',
      role: 'Professional Trader',
      avatar: '👨‍💻',
      text: 'The real-time market data and advanced trading features have significantly improved my trading performance. The platform\'s security measures give me peace of mind.'
    },
    {
      name: '<PERSON>',
      role: 'Crypto Fund Manager',
      avatar: '👩‍💼',
      text: 'BullSeed\'s institutional-grade tools transformed our trading strategy. The API integration and automated features have saved us countless hours.'
    }
  ];

  return (
    <section className="testimonials" id="testimonials">
      <div className="testimonials-container">
        <div className="testimonials-header">
          <h2 className="testimonials-title">Trusted by Traders</h2>
          <p className="testimonials-subtitle">Join thousands of satisfied traders on BullSeed</p>
        </div>
        
        <div className="testimonials-grid">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="testimonial-card">
              <div className="testimonial-avatar">{testimonial.avatar}</div>
              <div className="testimonial-content">
                <p className="testimonial-text">"{testimonial.text}"</p>
                <div className="testimonial-author">
                  <h4 className="testimonial-name">{testimonial.name}</h4>
                  <p className="testimonial-role">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
