import React from 'react';

const Features = () => {
  const features = [
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 3v18h18"/>
          <path d="M7 12l4-4 4 4 6-6"/>
        </svg>
      ),
      title: 'Advanced Trading Interface',
      description: 'Professional-grade trading tools with real-time market data and advanced charting capabilities.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
        </svg>
      ),
      title: 'Portfolio Management',
      description: 'Track your investments and monitor your gains with our comprehensive portfolio management system.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
        </svg>
      ),
      title: 'Security & Verification',
      description: 'Industry-leading security measures with KYC verification process to protect your assets.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
        </svg>
      ),
      title: 'Performance Analytics',
      description: 'Detailed analytics and credit scoring system to help you make informed trading decisions.'
    }
  ];

  return (
    <section className="features" id="features">
      <div className="features-container">
        <div className="features-content">
          <div className="features-left">
            <div className="features-header">
              <h2 className="features-title">
                Advanced Trading<br />
                <span className="highlight">Features & Tools</span>
              </h2>
              <p className="features-description">
                Experience professional-grade trading tools and features designed for both novice and experienced crypto traders.
              </p>
            </div>

            <div className="features-list">
              {features.map((feature, index) => (
                <div key={index} className="feature-item">
                  <div className="feature-icon">{feature.icon}</div>
                  <div className="feature-content">
                    <h3 className="feature-title">{feature.title}</h3>
                    <p className="feature-description">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="features-right">
            <div className="dashboard-preview">
              <img src="/img 2.png" alt="BullSeed Dashboard" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
