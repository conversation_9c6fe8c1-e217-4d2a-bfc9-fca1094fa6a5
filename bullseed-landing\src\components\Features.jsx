import React from 'react';

const Features = () => {
  const features = [
    {
      icon: '📊',
      title: 'Advanced Trading Interface',
      description: 'Professional-grade trading tools with real-time market data and advanced charting capabilities.'
    },
    {
      icon: '💼',
      title: 'Portfolio Management',
      description: 'Track your investments and monitor your gains with our comprehensive portfolio management system.'
    },
    {
      icon: '🔒',
      title: 'Security & Verification',
      description: 'Industry-leading security measures with KYC verification process to protect your assets.'
    },
    {
      icon: '📈',
      title: 'Performance Analytics',
      description: 'Detailed analytics and credit scoring system to help you make informed trading decisions.'
    }
  ];

  return (
    <section className="features" id="features">
      <div className="features-container">
        <div className="features-header">
          <h2 className="features-title">
            Advanced Trading<br />
            <span className="highlight">Features & Tools</span>
          </h2>
          <p className="features-description">
            Experience professional-grade trading tools and features designed for both novice and experienced crypto traders.
          </p>
        </div>
        
        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">{feature.icon}</div>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
